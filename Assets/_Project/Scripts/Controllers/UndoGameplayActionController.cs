using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

public class UndoGameplayActionController : BaseController
{
    public UndoGameplayActionController()
    {
        SubscribeToPlayerActions();
    }

    private void RecordStreakIncrease(StreakIncreasedEvent evt)
    {
        Game.DataManager.Session.UndoAction.
    }

    private void SubscribeToPlayerActions()
    {
        Subscribe<DeckCardPlayedEvent>(evt => SaveSnapshot(evt));
        Subscribe<PuzzleCardPlayedEvent>(evt => SaveSnapshot(evt));
    }

    private void SaveSnapshot(IEvent playerActionEvent)
    {
        Game.DataManager.Session.UndoAction.Snapshot = new UndoSnapshotData()
        {
            WildCardData = Game.DataManager.Session.WildCard.Clone(),
            StreakData = Game.DataManager.Session.Streak,
            Cards = Game.DataManager.Session.CurrentPuzzle.PlayablePuzzleCards
        };
    }
    
    private void ActivateUndo()
    {
        //  Iterate over data
        //  Allow view to control visual sequence based on the undo data
        //  Clear undo data
        //  Allow player to perform actions again
    }
}
